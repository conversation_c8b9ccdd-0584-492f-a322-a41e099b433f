# Giggle Backend

A backend server built with Express and TypeScript that provides API endpoints for the Giggle application.

## Overview

This backend application uses:
- **Express**: Web framework for handling HTTP requests
- **TypeScript**: For type-safe code
- **MongoDB**: Database for storing application data
- **Appwrite**: Backend-as-a-service for additional functionalities
- **dotenv**: For environment variable management

## Prerequisites

Before running the application, make sure you have the following installed:
- Node.js (v16 or newer)
- npm or yarn
- MongoDB instance (local or remote)
- Appwrite account (if using Appwrite services)

## Setup Instructions

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=5000
   MONGODB_URI=your_mongodb_connection_string
   APPWRITE_ENDPOINT=your_appwrite_endpoint
   APPWRITE_PROJECT_ID=your_appwrite_project_id
   APPWRITE_API_KEY=your_appwrite_api_key
   ```

## Running the Application

### Development
```
npm run dev
```

### Production
```
npm run build
npm start
```

## API Endpoints

The following API endpoints are currently available:

### User Routes
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

## Graceful Shutdown

The server handles graceful shutdown on SIGINT and SIGTERM signals, closing the database connection properly.

## Project Structure

```
giggle/
├── src/
│   ├── config/
│   │   └── db.ts         # Database configuration
│   ├── routes/
│   │   ├── example.routes.ts
│   │   ├── index.ts      # Route registration
│   │   └── user.routes.ts
│   └── index.ts          # Main application entry
├── package.json
├── tsconfig.json
└── .env
```

## Technologies Used

- Node.js
- TypeScript
- Express
- MongoDB
- Appwrite
- CORS
- dotenv
