import { ObjectId } from "mongodb";

// Location interface for geographical coordinates
export interface Location {
	latitude: number;
	longitude: number;
	address?: string;
}




export interface User {
	_id?: string | ObjectId;
	appwriteId: string;
	email: string;
	name: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	userType: "seeker" | "provider";
	createdAt: Date;
	updatedAt: Date;
	isDeleted: boolean;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
		// Add any additional profile fields as needed
	};
}

export interface CreateUserDto {
	appwriteId: string;
	email: string;
	name: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	userType: string;
	createdAt?: Date;
	updatedAt?: Date;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

export interface UpdateUserDto {
	name?: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

// Helper type for MongoDB document with ObjectId
export type UserDocument = Omit<User, "_id"> & {
	_id: ObjectId;
};
