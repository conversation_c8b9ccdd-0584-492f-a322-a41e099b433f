import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import { closeDBConnection, connectToDB } from "./config/db";
import { registerRoutes } from "./routes";

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;
declare global {
  namespace Express {
    interface Request {
      user: {
        id: string;
        email: string;
        type: string;
      };
    }
  }
}

const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(",") || [];
app.use(
  cors({
    origin: (origin, callback) => {
      if (!origin) return callback(null, true);
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      return callback(new Error("Not allowed by CORS"));
    },
    credentials: true,
  })
);

app.use(express.json());

// Register all routes
registerRoutes(app);

// Start server after DB connects
connectToDB().then(() => {
  const server = app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });

  // Handle graceful shutdown
  process.on("SIGINT", async () => {
    console.log("[SIGINT] Shutting down server...");
    server.close(async () => {
      await closeDBConnection();
      process.exit(0);
    });
  });

  process.on("SIGTERM", async () => {
    console.log("[SIGTERM] Shutting down server...");
    server.close(async () => {
      await closeDBConnection();
      process.exit(0);
    });
  });
});
