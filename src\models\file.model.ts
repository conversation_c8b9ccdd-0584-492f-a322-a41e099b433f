import { ObjectId } from "mongodb";

export type FileType = "resume" | "cover_letter" | "portfolio" | "certificate" | "image" | "document" | "other";

export interface File {
	_id?: string | ObjectId;
	fileId: string; // Unique identifier for the file (could be from cloud storage)
	fileName: string;
	fileType: FileType;
	mimeType: string; // e.g., "application/pdf", "image/jpeg"
	fileUrl: string; // URL to access the file
	size: number; // File size in bytes
	uploadedBy: string | ObjectId; // Reference to either Seeker._id or Provider._id who uploaded the file
	uploaderType: "seeker" | "provider"; // Type of user who uploaded the file
	active: boolean; // Whether the file is currently active/available
	isDeleted: boolean;
	uploadedAt: Date;
	createdAt: Date;
	updatedAt: Date;
	metadata?: {
		description?: string;
		tags?: string[];
		originalName?: string;
		uploadSource?: string; // e.g., "web", "mobile", "api"
	};
}

export interface CreateFileDto {
	fileId: string;
	fileName: string;
	fileType: FileType;
	mimeType: string;
	fileUrl: string;
	size: number;
	userId: string;
	uploaderType: "seeker" | "provider";
	metadata?: {
		description?: string;
		tags?: string[];
		originalName?: string;
		uploadSource?: string;
	};
}

export interface UpdateFileDto {
	fileName?: string;
	fileType?: FileType;
	active?: boolean;
	metadata?: {
		description?: string;
		tags?: string[];
		originalName?: string;
		uploadSource?: string;
	};
}

// Helper type for MongoDB document with ObjectId
export type FileDocument = Omit<File, "_id"> & {
	_id: ObjectId;
};
