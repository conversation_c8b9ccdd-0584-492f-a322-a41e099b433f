import { NextFunction, Request, Response } from "express";
import { findById } from "../services/user.service";
import { verifyAppwriteUser } from "../utils/appwriteFunctions";

export const authenticateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      res.status(401).json({ error: "Missing token" });
      return;
    }

    const jwtToken = authHeader.trim().split(" ")[1];
    console.log("Received JWT token:", jwtToken);
    if (!jwtToken) {
      res.status(401).json({ error: "Missing token" });
      return;
    }
    const user = await verifyAppwriteUser(jwtToken);
    console.log("Authenticated user:", user);

    if (!user) {
      res.status(401).json({ error: "Invalid or expired token" });
      return;
    }

    console.log("User ID:", user.$id);
    // const userDetails = await findById(user.$id);
    // console.log("User details:", userDetails);
    // if (!userDetails) {
    //   res.status(404).json({ error: "User not found1" });
    //   return;
    // }
    // console.log("User details found:", userDetails);

    req.user = {
      id: user.$id,
      type: "SEEKER",
      email: user.email,
    };
    // console.log("User set in request:", req.user);
    next();
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: "Internal server error" });
  }
};
