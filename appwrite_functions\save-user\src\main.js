import { Client, Users } from 'node-appwrite';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
dotenv.config();

export default async function main(context) {
  const { req, res } = context;
  const appwrite = new Client()
    .setEndpoint(process.env.APPWRITE_ENDPOINT)
    .setProject(process.env.APPWRITE_PROJECT_ID)
    .setKey(process.env.APPWRITE_API_KEY);

  const users = new Users(appwrite);

  // MongoDB connection URI (add this in environment variables on Appwrite Console)
  const mongoUri = process.env.MONGO_URI;
  const mongoClient = new MongoClient(mongoUri);

  try {
    // Parse the payload (user creation trigger body)
    const body = typeof req.body === 'string' ? JSON.parse(req.body) : req.body;
    const userId = body?.userId || body?.$id;

    if (!userId) {
      return res.json({ success: false, message: 'User ID not provided.' }, 400);
    }

    // Fetch user details from Appwrite
    const user = await users.get(userId);

    // Connect to MongoDB and insert user
    await mongoClient.connect();
    const db = mongoClient.db('appwrite_users'); // or your DB name
    const collection = db.collection('users');

    const userData = {
      _id: user.$id,
      email: user.email,
      name: user.name || 'Unnamed',
      createdAt: new Date(user.registration),
      status: user.status,
      labels: user.labels || [],
    };

    await collection.insertOne(userData);

    return res.json({ success: true, message: 'User stored in MongoDB.' });

  } catch (error) {
    console.error('Error:', error);
    return res.json({ success: false, message: error.message }, 500);
  } finally {
    await mongoClient.close();
  }
}