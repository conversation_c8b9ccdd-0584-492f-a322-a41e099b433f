import express from "express";
import {
	createGig,
	getProviderGigs,
	updateGig,
	deleteGig,
	getGig,
} from "../controllers/provider.controller";

const router = express.Router();

// Provider gig management routes
// Note: All routes are already protected by authenticateUser and providerAuthMiddleware in index.ts

// Get all gigs for the provider
router.get("/gigs", getProviderGigs);

// Create a new gig (List Your Gig)
router.post("/gigs", createGig);

// Get a specific gig
router.get("/gigs/:gigId", getGig);

// Update a specific gig
router.patch("/gigs/:gigId", updateGig);

// Delete a specific gig
router.delete("/gigs/:gigId", deleteGig);

export default router;
