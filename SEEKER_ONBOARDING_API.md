  # Seeker Onboarding API Documentation

  This document describes the API endpoints for the seeker onboarding flow in the Giggle application.

  ## Base URL
  ```
  http://localhost:6969/api/seeker
  ```

  ## Authentication
  All endpoints require authentication via JW<PERSON> token in the Authorization header:
  ```
  Authorization: Bearer <your_jwt_token>
  ```

  ## Onboarding Flow

  The seeker onboarding consists of 5 main steps as shown in the mobile app screenshots:

  ### 1. Initial Profile Creation
  **POST** `/profile`

  Creates the initial seeker profile with basic required information.

  **Request Body:**
  ```json
  {
    "name": "<PERSON>",
    "phoneNumber": "+91 9876543210",
    "educationLevel": "bachelor",
    "employmentStatus": "unemployed"
  }
  ```

  ### 2. Basic Information (Step 1)
  **PATCH** `/onboarding/basic-info`

  Updates basic personal information including name, date of birth, gender, and phone number.

  **Request Body:**
  ```json
  {
    "name": "<PERSON>",
    "dateOfBirth": "2000-06-15",
    "gender": "male",
    "phoneNumber": "+91 9876543210"
  }
  ```

  ### 3. Location Preferences (Step 2)
  **PATCH** `/onboarding/location`

  Updates location preferences and current location details.

  **Request Body:**
  ```json
  {
    "preferences": {
      "locations": ["Mumbai", "Delhi", "Bangalore"],
      "remoteWork": true,
      "willingToRelocate": true
    }
  }
  ```

  ### 4. Education Details (Step 3)
  **PATCH** `/onboarding/education`

  Updates education information including current education level, degree details, and university.

  **Request Body:**
  ```json
  {
    "educationLevel": "bachelor",
    "degree": "Bachelor of Technology",
    "specialization": "Computer Science",
    "completionYear": "2025",
    "university": "Indian Institute of Technology"
  }
  ```

  ### 5. Skills (Step 4)
  **PATCH** `/onboarding/skills`

  Updates the seeker's skills with predefined options and custom skills.

  **Request Body:**
  ```json
  {
    "skills": [
      {
        "name": "customer service",
        "level": "intermediate",
        "yearsOfExperience": 2
      },
      {
        "name": "Photography",
        "level": "advanced",
        "yearsOfExperience": 3
      }
    ]
  }
  ```

  ### 6. Resume Upload (Step 5)
  **PATCH** `/onboarding/resume`

  Uploads or links the seeker's resume.

  **Request Body:**
  ```json
  {
    "resumeLink": "https://example.com/resume.pdf",
    "resumeFileId": "file_123456789"
  }
  ```

  ## Additional Endpoints

  ### Get Seeker Profile
  **GET** `/profile`

  Retrieves the complete seeker profile.

  **Response:**
  ```json
  {
    "success": true,
    "data": {
      "_id": "...",
      "appwriteId": "...",
      "email": "<EMAIL>",
      "name": "John Seeker",
      "phoneNumber": "+91 9876543210",
      "dateOfBirth": "2000-06-15T00:00:00.000Z",
      "gender": "male",
      "educationLevel": "bachelor",
      "employmentStatus": "unemployed",
      "skills": [...],
      "preferences": {...},
      "createdAt": "...",
      "updatedAt": "..."
    }
  }
  ```

  ### Update Complete Profile
  **PATCH** `/profile`

  Updates the complete seeker profile with any combination of fields.

  ## Data Types

  ### Education Levels
  - `no_formal_education`
  - `primary`
  - `secondary`
  - `high_school`
  - `diploma`
  - `bachelor`
  - `master`
  - `phd`
  - `other`

  ### Employment Status
  - `unemployed`
  - `employed`
  - `self_employed`
  - `student`
  - `retired`
  - `other`

  ### Gender Options
  - `male`
  - `female`
  - `other`
  - `prefer_not_to_say`

  ### Skill Levels
  - `beginner`
  - `intermediate`
  - `advanced`
  - `expert`

  ## Error Responses

  All endpoints return standardized error responses:

  ```json
  {
    "success": false,
    "error": "Error message description"
  }
  ```

  Common HTTP status codes:
  - `200` - Success
  - `201` - Created
  - `400` - Bad Request
  - `401` - Unauthorized
  - `403` - Forbidden
  - `404` - Not Found
  - `409` - Conflict (already exists)
  - `500` - Internal Server Error

  ## Testing

  Use the provided `test.http` file to test all endpoints with sample data. Make sure to replace the JWT token with a valid one from your authentication system.
